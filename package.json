{"name": "dashboard-tif-uin-suska", "private": true, "version": "1.0.1", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@fontsource/geist-sans": "^5.1.0", "@radix-ui/react-accordion": "^1.2.2", "@radix-ui/react-alert-dialog": "^1.1.5", "@radix-ui/react-avatar": "^1.1.2", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-collapsible": "^1.1.2", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.5", "@radix-ui/react-hover-card": "^1.1.14", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-navigation-menu": "^1.2.4", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.1", "@radix-ui/react-slider": "^1.3.2", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-toast": "^1.2.6", "@radix-ui/react-tooltip": "^1.1.7", "@react-spring/web": "^9.7.5", "@tanstack/react-query": "^5.66.0", "axios": "^1.7.9", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.0", "date-fns": "^3.6.0", "embla-carousel-react": "^8.5.2", "framer-motion": "^12.0.6", "gsap": "^3.12.7", "jspdf": "^2.5.2", "jspdf-autotable": "^3.8.4", "jwt-decode": "^4.0.0", "leaflet": "^1.9.4", "lucide-react": "^0.474.0", "motion": "^12.6.0", "ogl": "^1.0.11", "oidc-client-ts": "^3.1.0", "react": "^18.3.1", "react-calendar": "^5.1.0", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-dropzone": "^14.3.8", "react-hook-form": "^7.56.3", "react-hot-toast": "^2.5.2", "react-leaflet": "^4.2.1", "react-oidc-context": "^3.2.0", "react-router-dom": "^7.1.5", "react-router-hash-link": "^2.4.3", "recharts": "^2.15.1", "sonner": "^2.0.5", "tailwind-merge": "^3.0.1", "tailwindcss-animate": "^1.0.7", "three": "^0.173.0", "webgl": "^0.0.7"}, "devDependencies": {"@eslint/js": "^9.17.0", "@types/leaflet": "^1.9.17", "@types/node": "^22.13.0", "@types/react": "^18.3.18", "@types/react-dom": "^18.3.5", "@types/react-router-hash-link": "^2.4.9", "@types/three": "^0.173.0", "@vitejs/plugin-react-swc": "^3.5.0", "autoprefixer": "^10.4.20", "eslint": "^9.17.0", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.16", "globals": "^15.14.0", "postcss": "^8.5.1", "tailwindcss": "^3.4.1", "typescript": "~5.6.2", "typescript-eslint": "^8.18.2", "vite": "^6.0.5"}}