@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --thumb-color: #e4e4e7;
    --thumb-color-hover: #d0d0d5;
    --background: 0 0% 100%;
    --foreground: 240 10% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 240 10% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 240 10% 3.9%;
    --primary: 240 5.9% 10%;
    --primary-foreground: 0 0% 98%;
    --secondary: 240 4.8% 95.9%;
    --secondary-foreground: 240 5.9% 10%;
    --muted: 240 4.8% 95.9%;
    --muted-foreground: 240 3.8% 46.1%;
    --accent: 240 4.8% 95.9%;
    --accent-foreground: 240 5.9% 10%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 5.9% 90%;
    --input: 240 5.9% 90%;
    --ring: 240 10% 3.9%;
    --radius: 0.5rem;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
  .dark {
    --thumb-color: #27272a;
    --thumb-color-hover: #333333;
    --background: 240 10% 3.9%;
    --foreground: 0 0% 98%;
    --card: 240 10% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 240 10% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 240 5.9% 10%;
    --secondary: 240 3.7% 15.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 240 3.7% 15.9%;
    --muted-foreground: 240 5% 64.9%;
    --accent: 240 3.7% 15.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 3.7% 15.9%;
    --input: 240 3.7% 15.9%;
    --ring: 240 4.9% 83.9%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}
@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

body {
  font-family: "Geist Sans";
}

/* Mengatur scroll bar secara keseluruhan */
::-webkit-scrollbar {
  width: 7px; /* Lebar scroll bar */
  height: 7px; /* Tinggi scroll bar untuk elemen horizontal */
}

/* Mengatur track (bagian belakang) dari scroll bar */
::-webkit-scrollbar-track {
  background-color: transparent; /* Transparan */
}

/* Mengatur thumb (bagian yang digerakkan) dari scroll bar */
::-webkit-scrollbar-thumb {
  background-color: var(--thumb-color); /* Warna thumb */
  border-radius: 25px; /* Sudut yang sedikit membulat */
}

/* Mengatur thumb saat hover */
::-webkit-scrollbar-thumb:hover {
  background-color: var(--thumb-color-hover); /* Warna thumb saat hover */
  cursor: pointer;
}

::-webkit-scrollbar-button:vertical:increment {
  background: transparent;
  height: 10px;
  width: 7px;
}

::-webkit-scrollbar-button:vertical:decrement {
  background: transparent;
  height: 10px;
  width: 7px;
}

.animated-gradient-text {
  position: relative;
  margin: 0;
  display: flex;
  max-width: fit-content;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  border-radius: 1.25rem;
  font-weight: 500;
  backdrop-filter: blur(10px);
  transition: box-shadow 0.5s ease-out;
  overflow: hidden;
  cursor: pointer;
}

.gradient-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-size: 300% 100%;
  animation: gradient linear infinite;
  border-radius: inherit;
  z-index: 0;
  pointer-events: none;
}

.gradient-overlay::before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  border-radius: inherit;
  width: calc(100% - 2px);
  height: calc(100% - 2px);
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  background-color: hsl(var(--background));
  z-index: -1;
}

@keyframes gradient {
  0% {
    background-position: 0% 50%;
  }

  50% {
    background-position: 100% 50%;
  }

  100% {
    background-position: 0% 50%;
  }
}

.text-content {
  display: inline-block;
  position: relative;
  z-index: 2;
  background-size: 300% 100%;
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
  animation: gradient linear infinite;
}

.circular-gallery {
  width: 100%;
  height: 100%;
  overflow: hidden;
  cursor: grab;
}

.circular-gallery:active {
  cursor: grabbing;
}

.particles-container {
  position: absolute;
  width: 100%;
  height: 100%;
}

/* Menyembunyikan ikon default */
input[type="date"]::-webkit-calendar-picker-indicator {
  opacity: 0; /* Menyembunyikan ikon */
  position: absolute;
  width: 100%;
}
input[type="time"]::-webkit-calendar-picker-indicator {
  opacity: 0; /* Menyembunyikan ikon */
  position: absolute;
  width: 100%;
}

/* Menambahkan ikon kustom */
.date-input {
  position: relative;
  display: inline-block;
}

.date-input input {
  padding-right: 30px; /* Ruang untuk ikon */
}
